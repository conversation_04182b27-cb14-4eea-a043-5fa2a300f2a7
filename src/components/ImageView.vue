<template>
  <div class="image-view">
    <canvas ref="canvasElement" :width="width" :height="height"></canvas>
    <img v-if="imageUrl && !canvasRef" :src="imageUrl" :alt="altText" :style="{ maxWidth: width + 'px', maxHeight: height + 'px' }" />
  </div>
</template>

<script setup>
import { ref, defineProps, onMounted, watch } from 'vue';

const props = defineProps({
  imageUrl: {
    type: String,
    default: null
  },
  canvasRefProp: {
    type: Object, // Should be a ref to a canvas element from parent
    default: null
  },
  width: {
    type: Number,
    default: 300
  },
  height: {
    type: Number,
    default: 200
  },
  altText: {
    type: String,
    default: 'Image'
  }
});

const canvasElement = ref(null);

// If a canvasRefProp is passed, this component might be used to display a canvas manipulated by the parent.
// Otherwise, it can display an image via imageUrl or draw on its own canvas.

const drawImageOnCanvas = (src) => {
  if (canvasElement.value && src) {
    const ctx = canvasElement.value.getContext('2d');
    const img = new Image();
    img.onload = () => {
      // Clear canvas before drawing new image
      ctx.clearRect(0, 0, props.width, props.height);
      // Scale image to fit canvas dimensions while maintaining aspect ratio
      const hRatio = props.width / img.width;
      const vRatio = props.height / img.height;
      const ratio = Math.min(hRatio, vRatio);
      const centerShift_x = (props.width - img.width * ratio) / 2;
      const centerShift_y = (props.height - img.height * ratio) / 2;
      ctx.drawImage(img, 0, 0, img.width, img.height,
                    centerShift_x, centerShift_y, img.width * ratio, img.height * ratio);
    };
    img.src = src;
  }
};

onMounted(() => {
  if (props.imageUrl && !props.canvasRefProp) {
    drawImageOnCanvas(props.imageUrl);
  }
  // If canvasRefProp is provided, the parent is responsible for drawing on it.
  // This component's canvas can still be used as a fallback or for other purposes.
});

watch(() => props.imageUrl, (newUrl) => {
  if (newUrl && !props.canvasRefProp) {
    drawImageOnCanvas(newUrl);
  } else if (!newUrl && canvasElement.value) {
    // Clear canvas if imageUrl is removed
    const ctx = canvasElement.value.getContext('2d');
    ctx.clearRect(0, 0, props.width, props.height);
  }
});

// Expose the canvas element for parent component to draw on if needed
// This is an alternative to passing canvasRefProp
// defineExpose({ canvasElement });

</script>

<style scoped>
.image-view {
  border: 1px solid #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
}
.image-view canvas {
  display: block; /* Default, but good to be explicit */
}
.image-view img {
  display: block;
  object-fit: contain; /* Ensures the whole image is visible */
}
</style>