<template>
  <div class="download-section">
    <div class="download-buttons" v-if="isDownloadable">
      <button @click="downloadPNG" class="download-btn png-btn">
        <span class="btn-icon">📥</span>
        下载 PNG
      </button>
      <button @click="downloadPiskel" class="download-btn piskel-btn">
        <span class="btn-icon">🎨</span>
        下载 Piskel
      </button>
    </div>
    <div v-else class="download-placeholder">
      <span class="placeholder-icon">📥</span>
      <p>生成像素化图片后可下载</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineOptions } from 'vue'
defineOptions({ name: 'DownloadButton' })

const props = defineProps({
  canvasElement: {
    type: [Object, HTMLCanvasElement],
    default: undefined,
  },
})

const realCanvas = computed(() => {
  // 支持ref、直接传递canvas和响应式ref
  if (!props.canvasElement) return null
  if (props.canvasElement instanceof HTMLCanvasElement) return props.canvasElement
  if (props.canvasElement.value instanceof HTMLCanvasElement) return props.canvasElement.value
  return null
})

const isDownloadable = computed(
  () => !!realCanvas.value && realCanvas.value.width > 0 && realCanvas.value.height > 0
)

// 下载PNG格式
const downloadPNG = () => {
  if (!realCanvas.value) {
    alert('没有可下载的图片')
    return
  }
  try {
    const link = document.createElement('a')
    link.download = `pixelated-image-${Date.now()}.png`
    link.href = realCanvas.value.toDataURL('image/png')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    console.error('PNG下载失败:', error)
    alert('下载失败，请重试')
  }
}

// 下载Piskel格式（JSON）
const downloadPiskel = () => {
  if (!realCanvas.value) {
    alert('没有可下载的图片')
    return
  }
  try {
    const canvas = realCanvas.value
    const piskelData = {
      modelVersion: 2,
      piskel: {
        name: `Pixelated Image ${Date.now()}`,
        description: 'Generated by Pixelator App',
        fps: 12,
        height: canvas.height,
        width: canvas.width,
        layers: [
          {
            name: 'Layer 1',
            opacity: 1,
            frameCount: 1,
            chunks: [
              {
                layout: [[0]],
                base64PNG: canvas.toDataURL('image/png'),
              },
            ],
          },
        ],
      },
    }
    const jsonString = JSON.stringify(piskelData, null, 2)
    const blob = new Blob([jsonString], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.download = `pixelated-image-${Date.now()}.piskel`
    link.href = url
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    setTimeout(() => URL.revokeObjectURL(url), 100)
  } catch (error) {
    console.error('Piskel下载失败:', error)
    alert('下载失败，请重试')
  }
}
</script>

<style scoped>
.download-section {
  width: 100%;
}

.download-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.download-btn {
  flex: 1;
  min-width: 120px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.png-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.png-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.piskel-btn {
  background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
  color: white;
}

.piskel-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a32a3 0%, #d91a72 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(111, 66, 193, 0.3);
}

.download-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-icon {
  font-size: 16px;
}

.download-placeholder {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.placeholder-icon {
  font-size: 32px;
  display: block;
  margin-bottom: 8px;
  opacity: 0.5;
}

.download-placeholder p {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

/* 添加按钮点击效果 */
.download-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .download-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .download-btn {
    min-width: 100%;
    padding: 10px 14px;
    font-size: 13px;
  }

  .btn-icon {
    font-size: 14px;
  }

  .download-placeholder {
    padding: 16px;
  }

  .placeholder-icon {
    font-size: 28px;
  }
}
</style>
