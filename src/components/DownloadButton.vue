<template>
  <div class="download-section">
    <div class="download-buttons" v-if="isDownloadable">
      <button @click="downloadPNG" class="download-btn png-btn">
        <span class="btn-icon">📥</span>
        下载 PNG
      </button>
      <button @click="downloadPiskel" class="download-btn piskel-btn">
        <span class="btn-icon">🎨</span>
        下载 Piskel
      </button>
    </div>
    <div v-else class="download-placeholder">
      <span class="placeholder-icon">📥</span>
      <p>生成像素化图片后可下载</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  canvasElement: {
    type: [Object, HTMLCanvasElement],
    default: undefined,
  },
})

const realCanvas = computed(() => {
  // 支持ref、直接传递canvas和响应式ref
  if (!props.canvasElement) return null
  if (props.canvasElement instanceof HTMLCanvasElement) return props.canvasElement
  if (props.canvasElement.value instanceof HTMLCanvasElement) return props.canvasElement.value
  return null
})

const isDownloadable = computed(
  () => !!realCanvas.value && realCanvas.value.width > 0 && realCanvas.value.height > 0
)

// 下载PNG格式
const downloadPNG = () => {
  if (!realCanvas.value) {
    alert('没有可下载的图片')
    return
  }
  try {
    const link = document.createElement('a')
    link.download = `pixelated-image-${Date.now()}.png`
    link.href = realCanvas.value.toDataURL('image/png')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    console.error('PNG下载失败:', error)
    alert('下载失败，请重试')
  }
}

// 下载Piskel格式（JSON）
const downloadPiskel = () => {
  if (!realCanvas.value) {
    alert('没有可下载的图片')
    return
  }
  try {
    const canvas = realCanvas.value
    const piskelData = {
      modelVersion: 2,
      piskel: {
        name: `Pixelated Image ${Date.now()}`,
        description: 'Generated by Pixelator App',
        fps: 12,
        height: canvas.height,
        width: canvas.width,
        layers: [
          {
            name: 'Layer 1',
            opacity: 1,
            frameCount: 1,
            chunks: [
              {
                layout: [[0]],
                base64PNG: canvas.toDataURL('image/png'),
              },
            ],
          },
        ],
      },
    }
    const jsonString = JSON.stringify(piskelData, null, 2)
    const blob = new Blob([jsonString], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.download = `pixelated-image-${Date.now()}.piskel`
    link.href = url
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    setTimeout(() => URL.revokeObjectURL(url), 100)
  } catch (error) {
    console.error('Piskel下载失败:', error)
    alert('下载失败，请重试')
  }
}
</script>

<style scoped>
.download-section {
  width: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 20px;
  padding: 28px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
}

.download-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
}

.download-btn {
  flex: 1;
  min-width: 160px;
  padding: 16px 24px;
  border: none;
  border-radius: 14px;
  font-size: 15px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.download-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.download-btn:hover:not(:disabled)::before {
  left: 100%;
}

.png-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.png-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 32px rgba(16, 185, 129, 0.4);
}

.piskel-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
}

.piskel-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #7c3aed 0%, #9333ea 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 32px rgba(139, 92, 246, 0.4);
}

.download-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-icon {
  font-size: 16px;
}

.download-placeholder {
  text-align: center;
  padding: 32px 24px;
  color: #64748b;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 2px dashed #cbd5e1;
  transition: all 0.3s ease;
}

.download-placeholder:hover {
  border-color: #94a3b8;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.placeholder-icon {
  font-size: 40px;
  display: block;
  margin-bottom: 12px;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

.download-placeholder p {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #475569;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

/* 添加按钮点击效果 */
.download-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .download-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .download-btn {
    min-width: 100%;
    padding: 10px 14px;
    font-size: 13px;
  }

  .btn-icon {
    font-size: 14px;
  }

  .download-placeholder {
    padding: 16px;
  }

  .placeholder-icon {
    font-size: 28px;
  }
}
</style>
