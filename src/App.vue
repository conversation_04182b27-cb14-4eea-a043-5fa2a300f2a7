<template>
  <div id="app-container">
    <header class="app-header">
      <h1>🎨 像素画转换器</h1>
      <p class="app-subtitle">将普通图片转换为精美的像素艺术作品</p>
    </header>
    <div class="main-layout">
      <!-- 左侧操作区 -->
      <aside class="sidebar">
        <ImageUploader @image-uploaded="handleImageUpload" />
        <ControlsPanel
          :initialPixelSize="pixelSize"
          :initialPalette="selectedPalette"
          :initialDithering="enableDithering"
          :initialTransparency="preserveTransparency"
          :initialContrast="contrast"
          :initialBrightness="brightness"
          @settings-changed="handleSettingsChange"
        />
        <div class="action-buttons">
          <button
            class="pixelate-btn"
            @click="pixelateImage"
            :disabled="!originalImageUrl || isLoading"
          >
            <span class="btn-icon">✨</span>
            {{ isLoading ? '处理中...' : '生成像素化图片' }}
          </button>
          <DownloadButton :canvasElement="pixelatedCanvas" />
        </div>
      </aside>
      <!-- 右侧图片与编辑区 -->
      <section class="content-area">
        <div class="image-panels">
          <div class="image-panel original">
            <h2 class="image-title">📷 原始图片</h2>
            <div class="image-container">
              <img
                v-if="originalImageUrl"
                :src="originalImageUrl"
                alt="原始图片"
                class="original-image"
              />
              <div v-else class="placeholder">
                <span class="placeholder-icon">🖼️</span>
                <p>请上传一张图片开始转换</p>
              </div>
            </div>
          </div>
          <div class="image-panel pixelated">
            <h2 class="image-title">🎨 像素化结果</h2>
            <div class="image-container">
              <canvas
                v-show="pixelatedCanvas"
                ref="pixelatedCanvasRef"
                class="pixelated-canvas"
              ></canvas>
              <div v-if="!pixelatedCanvas" class="placeholder">
                <span class="placeholder-icon">✨</span>
                <p>像素化结果将在这里显示</p>
              </div>
            </div>
            <PixelEditor
              v-if="pixelatedCanvas"
              :canvasElement="pixelatedCanvas"
              :palette="selectedPalette ? palettes[selectedPalette] : []"
              @canvas-updated="handleCanvasUpdate"
            />
          </div>
        </div>
      </section>
    </div>
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p>正在处理图片...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import ImageUploader from './components/ImageUploader.vue'
import ControlsPanel from './components/ControlsPanel.vue'
import DownloadButton from './components/DownloadButton.vue'
import PixelEditor from './components/PixelEditor.vue'

const originalImageUrl = ref<string | null>(null)
const pixelatedCanvas = ref<HTMLCanvasElement | undefined>(undefined)
const pixelatedCanvasRef = ref<HTMLCanvasElement | null>(null)
const isLoading = ref(false)
const pixelSize = ref(16)
const selectedPalette = ref<keyof typeof palettes | ''>('')
const enableDithering = ref(false)
const preserveTransparency = ref(true)
const contrast = ref(1)
const brightness = ref(1)

const palettes: Record<string, number[][]> = {
  gameboy: [
    [15, 56, 15],
    [48, 98, 48],
    [139, 172, 15],
    [155, 188, 15],
  ],
  nes: [
    [124, 124, 124],
    [0, 0, 252],
    [0, 0, 188],
    [68, 40, 188],
    [148, 0, 132],
    [168, 0, 32],
    [168, 16, 0],
    [136, 20, 0],
    [80, 48, 0],
    [0, 120, 0],
    [0, 104, 0],
    [0, 88, 0],
    [0, 64, 88],
    [0, 0, 0],
    [188, 188, 188],
    [0, 120, 248],
    [0, 88, 248],
    [104, 68, 252],
    [216, 0, 204],
    [228, 0, 88],
    [248, 56, 0],
    [228, 92, 16],
    [172, 124, 0],
    [0, 184, 0],
    [0, 168, 0],
    [0, 168, 68],
    [0, 136, 136],
    [248, 248, 248],
    [60, 188, 252],
    [104, 136, 252],
    [152, 120, 248],
    [248, 120, 248],
    [248, 88, 152],
    [248, 120, 88],
    [252, 160, 68],
    [248, 184, 0],
    [184, 248, 24],
    [88, 216, 84],
    [88, 248, 152],
    [0, 232, 216],
    [120, 120, 120],
    [252, 252, 252],
    [164, 228, 252],
    [184, 184, 248],
    [216, 184, 248],
    [248, 184, 248],
    [248, 164, 192],
    [248, 184, 172],
    [252, 216, 168],
    [248, 216, 120],
    [216, 248, 120],
    [184, 248, 184],
    [184, 248, 216],
    [0, 252, 252],
    [248, 216, 248],
  ],
  cga: [
    [0, 0, 0],
    [85, 255, 255],
    [255, 85, 255],
    [255, 255, 255],
  ],
  grayscale: [
    [0, 0, 0],
    [36, 36, 36],
    [72, 72, 72],
    [108, 108, 108],
    [144, 144, 144],
    [180, 180, 180],
    [216, 216, 216],
    [255, 255, 255],
  ],
  sepia: [
    [112, 66, 20],
    [149, 102, 49],
    [196, 143, 78],
    [222, 184, 135],
    [245, 222, 179],
    [255, 248, 220],
  ],
  neon: [
    [255, 0, 255],
    [0, 255, 255],
    [255, 255, 0],
    [255, 0, 0],
    [0, 255, 0],
    [0, 0, 255],
    [255, 128, 0],
    [128, 255, 0],
  ],
  pastel: [
    [255, 179, 186],
    [255, 223, 186],
    [255, 255, 186],
    [186, 255, 201],
    [186, 225, 255],
    [186, 186, 255],
    [255, 186, 255],
    [255, 255, 255],
  ],
}

const handleImageUpload = (file: File | null) => {
  if (file) {
    originalImageUrl.value = URL.createObjectURL(file)
    clearPixelatedCanvas()
  } else {
    originalImageUrl.value = null
    clearPixelatedCanvas()
  }
}

const handleSettingsChange = (settings: {
  pixelSize: number
  palette: keyof typeof palettes | ''
  dithering: boolean
  transparency: boolean
  contrast: number
  brightness: number
}) => {
  pixelSize.value = settings.pixelSize
  selectedPalette.value = settings.palette
  enableDithering.value = settings.dithering
  preserveTransparency.value = settings.transparency
  contrast.value = settings.contrast
  brightness.value = settings.brightness
}

const handleCanvasUpdate = (updatedCanvas: HTMLCanvasElement) => {
  if (pixelatedCanvasRef.value && updatedCanvas) {
    const ctx = pixelatedCanvasRef.value.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, pixelatedCanvasRef.value.width, pixelatedCanvasRef.value.height)
      ctx.drawImage(updatedCanvas, 0, 0)
      pixelatedCanvas.value = updatedCanvas
    }
  }
}

const clearPixelatedCanvas = () => {
  pixelatedCanvas.value = undefined
  if (pixelatedCanvasRef.value) {
    const ctx = pixelatedCanvasRef.value.getContext('2d')
    if (ctx) ctx.clearRect(0, 0, pixelatedCanvasRef.value.width, pixelatedCanvasRef.value.height)
  }
}

const pixelateImage = async () => {
  if (!originalImageUrl.value) {
    alert('请先上传图片')
    return
  }
  isLoading.value = true
  try {
    const img = new window.Image()
    img.crossOrigin = 'anonymous'
    img.onload = () => {
      processImage(img)
    }
    img.onerror = () => {
      isLoading.value = false
      alert('图片加载失败，请重试')
    }
    img.src = originalImageUrl.value
  } catch (error) {
    isLoading.value = false
    console.error('像素化处理错误:', error)
    alert('处理失败，请重试')
  }
}

const processImage = (img: HTMLImageElement) => {
  try {
    const maxWidth = 600
    const maxHeight = 450
    let outputWidth = img.width
    let outputHeight = img.height
    const aspectRatio = img.width / img.height
    if (outputWidth > maxWidth) {
      outputWidth = maxWidth
      outputHeight = outputWidth / aspectRatio
    }
    if (outputHeight > maxHeight) {
      outputHeight = maxHeight
      outputWidth = outputHeight * aspectRatio
    }
    outputWidth = Math.round(outputWidth)
    outputHeight = Math.round(outputHeight)
    nextTick(() => {
      const canvas = pixelatedCanvasRef.value
      if (!canvas) {
        isLoading.value = false
        return
      }
      canvas.width = outputWidth
      canvas.height = outputHeight
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        isLoading.value = false
        return
      }
      ctx.imageSmoothingEnabled = false
      applyPixelEffect(ctx, img, outputWidth, outputHeight)
      // 关键：生成后强制刷新 pixelatedCanvas
      pixelatedCanvas.value = canvas
      isLoading.value = false
    })
  } catch (error) {
    isLoading.value = false
    console.error('图片处理错误:', error)
    alert('处理失败，请重试')
  }
}

const applyPixelEffect = (
  ctx: CanvasRenderingContext2D,
  img: HTMLImageElement,
  outputWidth: number,
  outputHeight: number
) => {
  const tempCanvas = document.createElement('canvas')
  const tempCtx = tempCanvas.getContext('2d')
  if (!tempCtx) return
  const blocksX = Math.max(1, Math.floor(outputWidth / pixelSize.value))
  const blocksY = Math.max(1, Math.floor(outputHeight / pixelSize.value))
  tempCanvas.width = blocksX
  tempCanvas.height = blocksY
  tempCtx.imageSmoothingEnabled = false
  tempCtx.drawImage(img, 0, 0, blocksX, blocksY)
  if (brightness.value !== 1 || contrast.value !== 1) {
    applyBrightnessContrast(tempCtx, blocksX, blocksY)
  }
  if (selectedPalette.value && palettes[selectedPalette.value]) {
    applyPalette(tempCtx, blocksX, blocksY, palettes[selectedPalette.value])
  }
  ctx.drawImage(tempCanvas, 0, 0, outputWidth, outputHeight)
}

const applyBrightnessContrast = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
  const imageData = ctx.getImageData(0, 0, width, height)
  const data = imageData.data
  for (let i = 0; i < data.length; i += 4) {
    let r = ((data[i] / 255 - 0.5) * contrast.value + 0.5) * 255
    let g = ((data[i + 1] / 255 - 0.5) * contrast.value + 0.5) * 255
    let b = ((data[i + 2] / 255 - 0.5) * contrast.value + 0.5) * 255
    r = r * brightness.value
    g = g * brightness.value
    b = b * brightness.value
    data[i] = Math.max(0, Math.min(255, r))
    data[i + 1] = Math.max(0, Math.min(255, g))
    data[i + 2] = Math.max(0, Math.min(255, b))
  }
  ctx.putImageData(imageData, 0, 0)
}

const applyPalette = (
  ctx: CanvasRenderingContext2D,
  width: number,
  height: number,
  palette: number[][]
) => {
  const imageData = ctx.getImageData(0, 0, width, height)
  const data = imageData.data
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i]
    const g = data[i + 1]
    const b = data[i + 2]
    const a = data[i + 3]
    if (preserveTransparency.value && a < 128) {
      continue
    }
    let closestColor: number[]
    if (enableDithering.value) {
      closestColor = findClosestColorWithDithering(r, g, b, palette, i, width)
    } else {
      closestColor = findClosestColor(r, g, b, palette)
    }
    data[i] = closestColor[0]
    data[i + 1] = closestColor[1]
    data[i + 2] = closestColor[2]
  }
  ctx.putImageData(imageData, 0, 0)
}

const findClosestColor = (r: number, g: number, b: number, palette: number[][]): number[] => {
  let minDistance = Infinity
  let closestColor = palette[0]
  for (const color of palette) {
    const distance = Math.sqrt(
      Math.pow(r - color[0], 2) + Math.pow(g - color[1], 2) + Math.pow(b - color[2], 2)
    )
    if (distance < minDistance) {
      minDistance = distance
      closestColor = color
    }
  }
  return closestColor
}

const findClosestColorWithDithering = (
  r: number,
  g: number,
  b: number,
  palette: number[][],
  pixelIndex: number,
  width: number
): number[] => {
  const x = pixelIndex % width
  const y = Math.floor(pixelIndex / width)
  const noise = ((x + y) % 2) * 16 - 8
  const adjustedR = Math.max(0, Math.min(255, r + noise))
  const adjustedG = Math.max(0, Math.min(255, g + noise))
  const adjustedB = Math.max(0, Math.min(255, b + noise))
  return findClosestColor(adjustedR, adjustedG, adjustedB, palette)
}
</script>

<style>
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  min-height: 100vh;
  padding: 10px;
  box-sizing: border-box;
}

* {
  box-sizing: border-box;
}

#app-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  background: #fff;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.15), 0 8px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  backdrop-filter: blur(10px);
  min-height: calc(100vh - 20px);
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 30px 20px;
  border-radius: 0 0 32px 32px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.12);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.app-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.15"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.app-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.8em;
  font-weight: 800;
  letter-spacing: 2px;
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-subtitle {
  margin: 0;
  font-size: 1.2em;
  opacity: 0.92;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.main-layout {
  display: flex;
  flex-direction: row;
  gap: 32px;
  padding: 36px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: calc(100vh - 200px);
}

.sidebar {
  flex: 0 0 360px;
  display: flex;
  flex-direction: column;
  gap: 28px;
  min-width: 320px;
  max-width: 380px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: fit-content;
  position: sticky;
  top: 24px;
}

.content-area {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.image-panels {
  display: flex;
  flex-direction: row;
  gap: 24px;
}

.image-panel {
  flex: 1 1 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 28px 24px 24px 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  min-width: 320px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.image-panel:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
}

.image-title {
  margin: 0 0 20px 0;
  color: #495057;
  font-size: 1.2em;
  font-weight: 700;
  letter-spacing: 1px;
  text-align: center;
  padding-bottom: 12px;
  border-bottom: 2px solid #f1f5f9;
}

.image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 360px;
  border-radius: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px dashed #e2e8f0;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}
.image-container:hover {
  border-color: #667eea;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  transform: scale(1.01);
}

.image-display,
.canvas-display {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.original-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.08);
}

.pixelated-canvas {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
  background: white;
}

.action-buttons {
  margin: 28px 0 16px 0;
  text-align: center;
}

.pixelate-btn {
  background: linear-gradient(135deg, #667eea 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 16px 36px;
  border-radius: 12px;
  font-size: 1.1em;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  min-width: 200px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.12);
  position: relative;
  overflow: hidden;
}

.pixelate-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.pixelate-btn:hover:not(:disabled)::before {
  left: 100%;
}

.pixelate-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #1ea085 100%);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.25);
}
.pixelate-btn:disabled {
  background: linear-gradient(135deg, #bfc8e6 0%, #a8b5d1 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-icon {
  font-size: 1.2em;
}

.placeholder {
  text-align: center;
  color: #94a3b8;
  padding: 60px 20px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border-radius: 12px;
  border: 1px dashed #cbd5e1;
}
.placeholder-icon {
  font-size: 64px;
  display: block;
  margin-bottom: 20px;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}
.placeholder p {
  margin: 0;
  font-size: 1.1em;
  font-weight: 500;
  color: #64748b;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(12px);
  animation: fadeIn 0.3s ease-out;
}
.loading-content {
  text-align: center;
  color: white;
  background: rgba(255, 255, 255, 0.15);
  padding: 60px 48px;
  border-radius: 24px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
}
.loading-spinner {
  width: 56px;
  height: 56px;
  border: 6px solid rgba(255, 255, 255, 0.2);
  border-top: 6px solid #667eea;
  border-right: 6px solid #20c997;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  margin: 0 auto 28px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.editor-section {
  margin-top: 24px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.07);
  border-radius: 14px;
  border: 1.5px solid rgba(102, 126, 234, 0.08);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.06);
  backdrop-filter: blur(8px);
}

@media (max-width: 1200px) {
  .main-layout {
    flex-direction: column;
    gap: 24px;
    padding: 24px 18px;
  }
  .image-panels {
    flex-direction: column;
    gap: 20px;
  }
  .sidebar {
    max-width: 100%;
    min-width: 0;
    position: static;
    flex: none;
    order: 2;
  }
  .content-area {
    gap: 20px;
    order: 1;
  }
  .image-container {
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  body {
    padding: 5px;
  }

  #app-container {
    width: 100%;
    border-radius: 16px;
    min-height: calc(100vh - 10px);
  }

  .app-header {
    padding: 24px 16px;
    border-radius: 0 0 20px 20px;
  }

  .app-header h1 {
    font-size: 2em;
    margin-bottom: 8px;
  }

  .app-subtitle {
    font-size: 1em;
  }

  .main-layout {
    flex-direction: column;
    gap: 16px;
    padding: 16px 12px;
    min-height: auto;
  }

  .sidebar {
    padding: 16px;
    gap: 16px;
    border-radius: 16px;
    order: 2;
  }

  .content-area {
    gap: 16px;
    order: 1;
  }

  .image-panel {
    padding: 16px;
    min-width: 0;
    border-radius: 16px;
  }

  .image-container {
    min-height: 200px;
  }

  .pixelate-btn {
    width: 100%;
    padding: 14px 24px;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  body {
    padding: 2px;
  }

  #app-container {
    border-radius: 12px;
  }

  .app-header {
    padding: 20px 12px;
    border-radius: 0 0 16px 16px;
  }

  .app-header h1 {
    font-size: 1.8em;
  }

  .main-layout {
    padding: 12px 8px;
    gap: 12px;
  }

  .sidebar {
    padding: 12px;
    gap: 12px;
  }

  .image-panel {
    padding: 12px;
  }

  .image-container {
    min-height: 180px;
  }

  .placeholder {
    padding: 40px 16px;
  }

  .placeholder-icon {
    font-size: 48px;
  }
}
</style>
