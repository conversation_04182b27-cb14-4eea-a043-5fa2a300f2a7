项目的重构，支持上传各种类型的图片并将其转化为支持二次修改的像素图片。以下是主要的改进和新增功能：

### 📁 图片上传功能增强

- 支持多种格式 ：扩展了图片上传支持，现在可以上传 JPEG、JPG、PNG、GIF、WebP、BMP、SVG 等多种格式
- 文件验证 ：添加了文件大小限制（10MB）和格式验证
- 用户友好提示 ：改进了拖拽上传的用户体验

### ⚙️ 像素化处理功能扩展

- 像素大小范围 ：从 4-32px 扩展到 2-64px，提供更灵活的像素化选项
- 新增调色板 ：添加了复古棕褐色、霓虹色彩、柔和色调三种新调色板
- 图像处理选项 ：
  - 抖动效果（Dithering）
  - 透明度保持
  - 对比度调整
  - 亮度调整

### 🎨 像素图片二次编辑功能

- 创建了 `PixelEditor.vue` 组件 ：提供完整的像素编辑功能
- 编辑模式切换 ：支持绘制和擦除模式
- 画笔工具 ：可调节画笔大小（1-5像素）
- 调色板集成 ：与主应用的调色板系统无缝集成
- 实时预览 ：编辑过程中实时显示修改效果
- 画布重置 ：一键恢复到原始像素化状态

### 🔧 技术实现亮点

- 组件化架构 ：将编辑器作为独立组件，便于维护和扩展
- 事件通信 ：通过 canvas-updated 事件实现编辑器与主应用的数据同步
- 画布管理 ：智能处理画布的创建、更新和下载功能
- 响应式设计 ：编辑器界面适配不同屏幕尺寸

### 🎯 用户体验优化

- 直观的界面 ：编辑器集成在像素化结果下方，操作流程自然
- 实时反馈 ：所有编辑操作都有即时的视觉反馈
- 无缝集成 ：编辑后的图片可以直接下载，支持 PNG 和 Piskel 格式

### 🚀 使用流程

1. 上传任意格式的图片
2. 调整像素化参数（大小、调色板、特效等）
3. 生成像素化图片
4. 使用集成的编辑器进行二次修改
5. 下载最终的像素艺术作品
